import {
	Button,
	Col,
	Dropdown,
	Icon,
	Input,
	Menu,
	message,
	Modal,
	Row,
	Spin,
	Table,
	Tag,
	Tooltip,
	DatePicker
} from "antd";
import DateFns from "date-fns";
import React from "react";
import Api from "../../api/api-handler";
import "../../static/css/common.css";
import "../../static/css/userManagement.css";
import scrollTo from "../scrollTo";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
import queryString from 'query-string';
import dayjs from 'dayjs';

const Search = Input.Search;
const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
const API = new Api({});
const { RangePicker } = DatePicker;
let timeoutVar;
let jobId;

export default class jobManagement extends React.Component {
	state = {
		apiParam: {},
		jobList: [],
		pagination: {},
		visible: false,
		viewPageData: "",
		pageloading: false,
		search: null,
		deleteModal: false,
		loading: false,
		confirmLoading: false,
		matchRecord: 0,
		isMatchRecord: false,
		apiKeyStatus: false,
		integratedWithStorage: false,
		integrationKeyStatus: false,
		shipmentNameToDelete: "",
		isMainAdmin: false,
		startDate: "",
		endDate: "",
		datePickerOpen: false,
		selectedDates: null
	};

	componentDidMount() {
		const queryParams = queryString.parse(window.location.search);
		let params = {
			search: "",
			orderBy: "created_at",
			pageNo: parseInt(queryParams.page) || "1",
			orderSequence: "DESC",
			pageSize: 10,
		};
		const userType = localStorage.getItem("userType") === "1" ? true : false;
		this.setState({ apiParam: params, isMainAdmin: userType }, () => this.fetchJobList());
		
		API.get(`api/admin/integrationKey/fetch`)
			.then((response) => {
				if (response) {
					if (response.data.status === "active") {
						this.setState({
							pageloading: false,
							integrationKeyStatus: true,
						});
					} else {
						this.setState({
							pageloading: false,
							integrationKeyStatus: false,
						});
					}
				}
			})
			.catch((error) => {
				this.setState({
					pageloading: false,
					loading: false,
				});
			});

		API.get(`api/open-api/apiKey/fetch`)
			.then((response) => {
				if (response) {
					if (response.status === 1) {
						if (response.data.isEnable === 1) {
							this.setState({
								apiKeyStatus: true,
							});
						}
						else {
							this.setState({
								apiKeyStatus: false,
							});
						}
					} else {
						console.log(response.message);
					}
				}
			})
			.catch((error) => {
				this.setState({ pageloading: false });
			});
	}

	fetchJobList = () => {
		const data = [];
		data["data"] = this.state.apiParam;
		this.setState({ pageloading: true });

		let apiUrl = "api/admin/shipment/list/?page_no=" +
			this.state.apiParam.pageNo +
			"&page_size=" +
			this.state.apiParam.pageSize +
			"&order_by_fields=" +
			this.state.apiParam.orderBy +
			"&order_sequence=" +
			this.state.apiParam.orderSequence +
			"&search=" +
			this.state.apiParam.search;

		if (this.state.startDate && this.state.endDate) {
			apiUrl += "&start_date=" + this.state.startDate + "&end_date=" + this.state.endDate;
		}
		
		API.get(apiUrl, data)
			.then((response) => {
				const pagination = { ...this.state.pagination };
				if (response) {
					if (response.status === 1) {
						pagination.total = response.data.count;
						this.setState({
							jobList: response.data.rows,
							pagination,
							pageloading: false,
							matchRecord: response.data.count,
						});
					} else {
						this.setState({
							usersList: response.data,
							pagination,
							pageloading: false,
						});
					}
				}
			})
			.catch((error) => {
				message.error(error.message);
				this.setState({
					pageloading: false,
				});
			});
	};

	handleChange = (pagination, filters, sorter) => {
		scrollTo();
		const pager = { ...this.state.pagination };
		pager.current = pagination.current;
		const queryParams = queryString.parse(window.location.search);
		queryParams.page = pagination.current;
		const newUrl = `${window.location.pathname}?${queryString.stringify(queryParams)}`;
		window.history.pushState(null, null, newUrl);
		let params = {
			...this.state.apiParam,
			pageNo: pagination.current,
			orderBy: sorter.order? sorter.field : "created_at",
			orderSequence: sorter.order === "ascend" ? "ASC" : "DESC",
			pageSize: pagination.pageSize,
		};

		this.setState(
			{
				pagination: pager,
				apiParam: params,
			},
			() => {
				this.state.search ? this.handleSearch(this.state.search) : this.fetchJobList();
			}
		);
	};

	addJob() {
		this.props.history.push(`${this.props.match.url}/add`);
	}

	delete(text) {
		if (text.storage_shipment_job_id) {
			this.setState({ integratedWithStorage: true });
		}
		this.setState({ shipmentNameToDelete: text.shipment_name, deleteModal: true });
		jobId = text.shipment_job_id;
	}

	viewQR(id, company_id, name, jobNumber, shipment) {
		this.props.history.push({
			pathname: `qr-code/${id}`,
			state: {
				company_id: company_id,
				name: name,
				jobNumber: jobNumber,
				shipment: shipment,
			},
		});
	}

	viewAssignWorker(id, company_id, name, jobNumber, shipment) {
		this.props.history.push({
			pathname: `assign-worker/${id}`,
			state: {
				company_id: company_id,
				name: name,
				jobNumber: jobNumber,
				shipment: shipment,
				jobId: id
			},
		});
	}

	edit(id) {
		this.props.history.push(`${this.props.match.url}/edit/${id}`);
	}

	view(id) {
		this.props.history.push(`${this.props.match.url}/view/${id}`);
	}

	email(id) {
		this.setState({ pageloading: true });
		API.post(`api/admin/mail_customer/${id}`)
			.then((response) => {
				if (response) {
					message.success(response.message);
					this.setState({ pageloading: false });
					this.fetchJobList();
				} else {
					message.error(response.message);
					this.setState({ pageloading: false });
				}
			})
			.catch((error) => {
				message.error(error.message);
				this.setState({
					pageloading: false,
				});
			});
	}

	handleCancel = () => {
		this.setState({
			deleteModal: false,
			confirmLoading: false,
		});
	};

	menus = (text) => {
		return (
			<Menu>
				<Menu.Item key='1'>
					<Tooltip title='Assign Worker'>
						<Button
							type='primary'
							className='c-btn c-round c-success'
							icon='usergroup-add'
							onClick={() =>
								this.viewAssignWorker(
									text.shipment_job_id,
									text.company_id,
									text.customer_job.full_name,
									text.job_number,
									text.shipment_type_for_shipment.name
								)
							}
						/>
					</Tooltip>
				</Menu.Item>
				<Menu.Item key='2'>
					<Tooltip title='Manage Labels'>
						<Button
							type='primary'
							className='c-btn c-round c-danger'
							icon='qrcode'
							onClick={() =>
								this.viewQR(
									text.shipment_job_id,
									text.company_id,
									text.customer_job.full_name,
									text.job_number,
									text.shipment_type_for_shipment.name
								)
							}
						/>
					</Tooltip>
				</Menu.Item>
				<Menu.Item key='3'>
					<Tooltip title='Edit'>
						<Button
							type='primary'
							className='c-btn c-round c-warning'
							icon='edit'
							onClick={() => this.edit(text.shipment_job_id)}></Button>
					</Tooltip>
				</Menu.Item>
				<Menu.Item key='4'>
					<Tooltip title='View'>
						<Button
							type='primary'
							className='c-btn c-round c-success'
							icon='eye'
							onClick={() => this.view(text.shipment_job_id)}></Button>
					</Tooltip>
				</Menu.Item>
				<Menu.Item key='5'>
					<Tooltip title={`Email Portal Invite ${text.email}`}>
						<Button
							type='primary'
							className='c-btn c-round c-info'
							icon='mail'
							onClick={() => this.email(text.shipment_job_id)}></Button>
					</Tooltip>
				</Menu.Item>
				<Menu.Item key='6'>
					<Tooltip title='Delete'>
						<Button
							type='primary'
							className='c-btn c-round c-danger'
							icon='delete'
							onClick={() => this.delete(text)}></Button>
					</Tooltip>
				</Menu.Item>
			</Menu>
		);
	};

	handleOk = () => {
		const id = jobId;
		this.setState({ confirmLoading: true });
		const deleteData = [];
		deleteData["data"] = { shipmentId: id };
		API.delete("api/admin/shipment/" + id, deleteData)
			.then((response) => {
				this.setState({
					deleteModal: false,
					confirmLoading: false,
				});
				if (response) {
					this.setState({
						deleteModal: false,
						confirmLoading: false,
					});
					this.fetchJobList();
					message.success(response.message);
				} else {
					this.setState({
						deleteModal: false,
						confirmLoading: false,
					});
					message.error(response.message);
					this.fetchJobList();
				}
			})
			.catch((error) => {
				message.error(error.message);
				this.setState({
					confirmLoading: false,
					deleteModal: false,
				});
				this.fetchJobList();
			});
	};

	handleStatusChange = (job_id) => {
		const statusData = [];
		statusData["data"] = { job_id: job_id };
		API.post("api/admin/job/change-job-status", statusData).then((response) => {
			if (response) {
				this.fetchJobList({
					page: this.state.pagination.current ? this.state.pagination.current : 1,
				});
			} else {
				message.error(response.message);
			}
		});
	};

	callback = () => {
		if (timeoutVar) {
			this.setState({ isMatchRecord: true });
			clearTimeout(timeoutVar);
			timeoutVar = null;
		}
		timeoutVar = setTimeout(this.fetchJobList, 1000);
	};

	handleSearch = (e) => {
		this.setState(
			{
				apiParam: {
					...this.state.apiParam,
					search: e,
					pageNo: 1,
				},
				search: e,
			},
			this.callback
		);
	};

	toDate = (date) => {
		let s = new Date(date).toLocaleTimeString("en-US", {
			day: "numeric",
			month: "short",
			year: "numeric",
			hour: "2-digit",
			minute: "2-digit",
		});
		return s;
	};

	handleDateRangeChange = (dates, dateStrings) => {
		this.setState({
			selectedDates: dates,
		});
		
		if (dates && dates.length > 0) {
			this.setState({
				startDate: dateStrings[0],
				endDate: dateStrings[1],
			});
		} else {
			this.setState({
				startDate: "",
				endDate: "",
			});
		}
	}

	handleDateSearch = () => {
		this.setState({
			apiParam: {
				...this.state.apiParam,
				pageNo: 1,
			},
			pagination: {
				...this.state.pagination,
				current: 1,
			},
			datePickerOpen: false
		}, () => {
			this.fetchJobList();
		});
	}

	handleDateCancel = () => {
		this.setState({
			selectedDates: null,
			startDate: "",
			endDate: "",
			apiParam: {
				...this.state.apiParam,
				pageNo: 1,
			},
			pagination: {
				...this.state.pagination,
				current: 1,
			},
			datePickerOpen: false
		}, () => {
			this.fetchJobList();
		});
	}

	handleOpenChange = (open) => {
		if (open) {
			this.setState({ datePickerOpen: open });
		}
	}

	render() {
		const { apiKeyStatus } = this.state;
		const columns = [
			{
				title: "Customer Name",
				dataIndex: "full_name",
				key: "full_name",
				align: "left",
				render: (text, record) => {
					return (
						<div>
							{record.customer_job && record.customer_job.full_name !== ""
								? record.customer_job.full_name
								: "-"}
						</div>
					);
				},
			},
			{
				title: "Shipment Number",
				dataIndex: "job_number",
				key: "job_number",
				align: "center",
				sorter: true,
				render: (text, record) => {
					return (
						<div>
							<a onClick={() => this.view(record.shipment_job_id)} > <strong>{record.job_number}</strong> </a>
						</div>
					);
				},
			},
			{
				title: "Created Date",
				dataIndex: "created_at",
				key: "created_at",
				align: "center",
				sorter: true,
				render: (text, record) => {
					return <div> {record.created_at ? this.toDate(record.created_at) : "-"} </div>
				}
			},
			{
				title: "Shipment Name",
				dataIndex: "shipment_name",
				key: "shipment_name",
				align: "left",
				sorter: true,
				render: (text, record) => {
					return (
						<div>
							<strong>{record.shipment_name}</strong>
						</div>
					);
				},
			},
			{
				title: "Shipment Type",
				dataIndex: "shipment_type",
				key: "shipment_type",
				align: "left",
				render: (text, record) => {
					return (
						<div>
							{record.shipment_type_for_shipment && record.shipment_type_for_shipment.name !== "" ? record.shipment_type_for_shipment.name : "-"}
						</div>
					);
				},
			},
			{
				title: "Shipment Tags",
				dataIndex: "shipment_tag",
				key: "shipment_tag",
				minWidth: "15%",
				align: "center",
				render: (record, text) => (
					<div key={text.shipment_job_id} style={{ display: "flex", flexWrap: "wrap" }}>
						{record
							? record.map((item, index) => (
								<Tag key={index} color={item.m2m_tag.color} style={{ marginTop: "5px" }}>
									{item.m2m_tag.name}
								</Tag>
							))
							: ""}
					</div>
				),
			},
			{
				title: "Shipment Status",
				dataIndex: "job_status",
				key: "shipment_job_status",
				align: "center",
				render: (text, record) => {
					return (
						<div>
							{record.is_job_complete_flag === 1 ?
								<div style={{ color: "green" }} >
									<b>Completed</b>
								</div>
								:
								<>
									{
										record.shipment_job_status && record.shipment_job_status.is_forced === 1 ? (
											<div style={{ color: "red" }}>
												<b>
													{record.shipment_job_status && record.shipment_job_status.name !== ""
														? record.shipment_job_status.name
														: ""}
												</b>
											</div>
										) : (
											<div>
												{record.local_shipment_job_status
													&& record.local_shipment_job_status
														.name !== ""
													? record.local_shipment_job_status
														.name
													: ""}
											</div>
										)
									}
								</>
							}
						</div>
					);
				},
			},
			{
				title: "Pickup Date",
				dataIndex: "pickup_date",
				key: "pickup_date",
				align: "center",
				sorter: true,
				render: (text, record) => {
					if (!record.pickup_date) return <div></div>;
					const dateOnly = record.pickup_date.split('T')[0];
					const [year, month, day] = dateOnly.split('-');
					return <div>{`${month}/${day}/${year}`}</div>;
				}
			},
			{
				title: "Estimated Delivery",
				dataIndex: "delivery_date",
				key: "delivery_date",
				align: "center",
				sorter: true,
				render: (text, record) => {
					return (
						<div>{record.delivery_date ? DateFns.format(record.delivery_date, "MM/DD/YYYY") : ""}</div>
					);
				},
			},
			{
				title: "Origin Address",
				dataIndex: "pickup_address",
				key: "pickup_address",
				minWidth: "15%",
				align: "left",
				render: (record, text) => {
					return (
						<div>
							<div
								style={!text.pickup_address2 ? { display: "flex" } : { display: "flex", flexWrap: "wrap", width: "130px" }}>
								<p style={{ margin: "0" }}>
									{text.pickup_address &&
										text.pickup_address !== "undefined" &&
										text.pickup_address !== "null"
										? text.pickup_address
										: ""}
								</p>
								&nbsp;
								<p style={{ margin: "0" }}>
									{text.pickup_address2 &&
										text.pickup_address2 !== "undefined" &&
										text.pickup_address2 !== "null"
										? text.pickup_address2
										: ""}
								</p>
							</div>
							<div style={{ display: "flex", flexWrap: "wrap", width: "130px" }}>
								<p style={{ margin: "0" }}>
									{text.pickup_city && text.pickup_city !== "undefined" && text.pickup_city !== "null"
										? text.pickup_city
										: ""}
								</p>
								<p style={{ margin: "0" }}>
									{text.pickup_state && text.pickup_state !== "undefined" && text.pickup_state !== "null"
										? ", " + text.pickup_state
										: ""}
								</p>
								&nbsp;&nbsp;
								<p style={{ margin: "0" }}>
									{text.pickup_zipcode &&
										text.pickup_zipcode !== "undefined" &&
										text.pickup_zipcode !== "null"
										? text.pickup_zipcode
										: ""}
								</p>
							</div>
							<div>
								{text.pickup_country &&
									text.pickup_country !== "undefined" &&
									text.pickup_country !== "null"
									? text.pickup_country
									: ""}
							</div>
						</div>
					);
				},
			},
			{
				title: "Destination Address",
				dataIndex: "delivery_address",
				key: "delivery_address",
				align: "left",
				render: (record, text) => {
					return (
						<div>
							<div
								style={
									!text.delivery_address2 ? { display: "flex" } : { display: "flex", flexWrap: "wrap", width: "130px" }
								}>
								<p style={{ margin: "0" }}>
									{text.delivery_address &&
										text.delivery_address !== "undefined" &&
										text.delivery_address !== "null"
										? text.delivery_address
										: ""}
								</p>
								&nbsp;
								<p style={{ margin: "0" }}>
									{text.delivery_address2 &&
										text.delivery_address2 !== "undefined" &&
										text.delivery_address2 !== "null"
										? text.delivery_address2
										: ""}
								</p>
							</div>
							<div style={{ display: "flex", flexWrap: "wrap", width: "130px" }}>
								<p style={{ margin: "0" }}>
									{text.delivery_city && text.delivery_city !== "undefined" && text.delivery_city !== "null"
										? text.delivery_city
										: ""}
								</p>
								<p style={{ margin: "0" }}>
									{text.delivery_state &&
										text.delivery_state !== "undefined" &&
										text.delivery_state !== "null"
										? ", " + text.delivery_state
										: ""}
								</p>
								&nbsp;&nbsp;
								<p style={{ margin: "0" }}>
									{text.delivery_zipcode &&
										text.delivery_zipcode !== "undefined" &&
										text.delivery_zipcode !== "null"
										? text.delivery_zipcode
										: ""}
								</p>
							</div>
							<div>
								{text.delivery_country &&
									text.delivery_country !== "undefined" &&
									text.delivery_country !== "null"
									? text.delivery_country
									: ""}
							</div>
						</div>
					);
				},
			},
			{
				title: "Invite Status",
				dataIndex: "customer_job",
				key: "customer_job",
				align: "center",
				render: (text, record) => {
					return (
						<div>
							<div
								style={
									record.customer_job.is_invited === "INVITED"
										? { color: "#00A0FF" }
										: record.customer_job.is_invited === "VIEWED"
											? { color: "#008744" }
											: record.customer_job.is_invited === "REGISTERED"
												? { color: "#2fd180" }
												: { color: "#C65146" }
								}>
								<strong>
									{record.customer_job.is_invited ? record.customer_job.is_invited : "PENDING"}
								</strong>
							</div>
						</div>
					);
				},
			},
			{
				title: "Action",
				key: "action",
				align: "center",
				render: (record, text) => {
					return (
						<div className='icons' style={{ textAlign: "center" }}>
							<Dropdown placement='bottomCenter' overlay={this.menus.bind(null, text)}>
								<Icon
									type='setting'
									theme='twoTone'
									twoToneColor='#3fa146'
									onClick={() => this.menus.bind(null, text)}
								/>
							</Dropdown>
						</div>
					);
				},
			},
		];

		return (
			<LayoutContentWrapper>
				<div className='top_header' style={{ height: "100%" }}>
					<Row>
						<Col sm={8}>
							<div style={{ display: "flex", alignItems: "center" }}>
								<h2 style={{ marginBottom: "0", marginRight: "15px" }}>
									<Icon type='car' />
									&emsp;Shipment Management
								</h2>
								{this.state.search ? (
									<p
										style={{
											margin: "0",
											color: "#303030",
										}}>
										Matching Records: {this.state.matchRecord}
									</p>
								) : (
									""
								)}
							</div>
						</Col>
						<Col sm={8}>
							<RangePicker
								value={this.state.selectedDates}
								onChange={(dates, dateStrings) => this.handleDateRangeChange(dates, dateStrings)}
								format="YYYY-MM-DD"
								open={this.state.datePickerOpen}
								onOpenChange={this.handleOpenChange}
								className="no-close-button"
								placeholder={['Start Date', 'End Date']}
								renderExtraFooter={() => (
									<div style={{ display: 'flex', justifyContent: 'space-between', gap: '8px', padding: '8px' }}>
										<Button 
											onClick={this.handleDateCancel}
											style={{ flex: 1 }}
										>
											Cancel
										</Button>
										<Button 
											type="primary" 
											onClick={this.handleDateSearch} 
											style={{ flex: 1 }}
											disabled={!this.state.selectedDates || this.state.selectedDates.length === 0}
										>
											Search
										</Button>
									</div>
								)}
							/>
						</Col>
						<Col sm={8}>
							<Row justify="space-evenly">
								<Col sm={12} style={{ textAlign: "center" }}>
									<Search
										placeholder="Search Shipment"
										onChange={(e) => this.handleSearch(e.target.value)}
										value={this.state.search}
										style={{ marginLeft: "10px", width: 200 }}
									/>
								</Col>
								{!this.state.isMainAdmin && (
									<Col sm={12}>
										<Button
											className="addButton"
											style={{ marginTop: 0 }}
											type="primary"
											onClick={() => this.addJob()}
										>
											+ Add Shipment
										</Button>
									</Col>
								)}
							</Row>
						</Col>
					</Row>
				</div>
				<Spin spinning={this.state.pageloading} indicator={antIcon}>
					<LayoutContent
						style={{
							marginTop: "10px",
						}}
					>
						<div
							style={{
								marginTop: "-1.5rem",
								overflowX: "auto",
							}}>
							<Table
								bordered={true}
								columns={columns}
								rowKey={(record) => record.shipment_job_id}
								pagination={{
									total: this.state.pagination.total,
									showSizeChanger: true,
									defaultPageSize: 10,
									pageSizeOptions: ["5", "10", "25"],
									current: this.state.apiParam.pageNo
								}}
								dataSource={this.state.jobList}
								onChange={this.handleChange}
							/>
						</div>
					</LayoutContent>
				</Spin>
				<Modal
					title='Are You Sure?'
					visible={this.state.deleteModal}
					onOk={this.handleOk}
					okText='Yes'
					cancelText='No'
					centered
					maskClosable={false}
					confirmLoading={this.state.confirmLoading}
					onCancel={this.handleCancel}>
					<p>{(this.state.integratedWithStorage && this.state.integrationKeyStatus) ? `Shipment integrated with mover storage are you sure you want to delete \"${this.state.shipmentNameToDelete}"\ Shipment ?` : `Are you sure you want to delete \"${this.state.shipmentNameToDelete}"\ Shipment ?`}</p>
				</Modal>
			</LayoutContentWrapper>
		);
	}
}