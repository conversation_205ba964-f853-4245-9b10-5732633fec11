# MOVER Inventory CMS - Developer Quick Reference

## Quick Start

### Prerequisites
- Node.js 14+ 
- npm or yarn
- Access to development environment APIs

### Installation & Setup
```bash
# Clone the repository
git clone <repository-url>
cd CMS

# Install dependencies
npm install

# Set up environment variables
cp .env.development .env.local
# Edit .env.local with your specific configuration

# Start development server
npm start
```

### Environment Variables
```bash
REACT_APP_BASE_URL=<API_BASE_URL>
REACT_APP_S3_URL=<S3_BUCKET_URL>
REACT_APP_MOVER_STORAGE_API_URL=<STORAGE_API_URL>
REACT_APP_GOOGLE_API_KEY=<GOOGLE_MAPS_API_KEY>
REACT_APP_SUPER_ADMIN_COMPANY_ID=-1
```

## Project Structure Quick Guide

### Key Directories
- `src/components/` - Reusable UI components
- `src/containers/` - Page-level components and routing
- `src/redux/` - State management (actions, reducers, sagas)
- `src/api/` - API client and HTTP handling
- `src/helpers/` - Utility functions
- `src/settings/` - App configuration and themes

### Important Files
- `src/index.js` - App entry point
- `src/dashApp.js` - Main app component with providers
- `src/router.js` - Route configuration
- `src/redux/store.js` - Redux store setup
- `src/api/api-handler.js` - Centralized API client

## Common Development Tasks

### Adding a New Page/Route
1. Create component in `src/containers/`
2. Add route to `src/containers/App/AppRouter.js`
3. Add navigation item to `src/containers/Sidebar/options.js`

### Creating API Calls
```javascript
// In component
import Api from "../../api/api-handler";
const API = new Api({});

// GET request
API.get("api/endpoint").then(response => {
  // Handle response
});

// POST request
const data = { data: formData };
API.post("api/endpoint", data).then(response => {
  // Handle response
});
```

### Redux State Management
```javascript
// Action
import appActions from "../../redux/app/actions";
const { changeCurrent } = appActions;

// In component
import { connect } from "react-redux";
const mapStateToProps = state => ({
  isLoggedIn: state.Auth.isLogin,
  companyId: state.Auth.companyID
});
export default connect(mapStateToProps, { changeCurrent })(Component);
```

### Form Handling with Ant Design
```javascript
import { Form, Input, Button } from "antd";

// Form submission
handleSubmit = (e) => {
  e.preventDefault();
  this.props.form.validateFieldsAndScroll((err, values) => {
    if (!err) {
      // Process form data
    }
  });
};

// Form field
const { getFieldDecorator } = this.props.form;
{getFieldDecorator('fieldName', {
  rules: [{ required: true, message: 'Field is required' }],
})(
  <Input placeholder="Enter value" />
)}
```

## Authentication Flow

### Login Process
1. User enters credentials at `/signin`
2. API call to `/api/admin/signin`
3. If multi-company user, redirect to `/signin-with-company`
4. Company selection and final authentication
5. Redux state update with user info
6. Redirect to dashboard

### Protected Routes
```javascript
// Route protection
const RestrictedRoute = ({ component: Component, isLoggedIn, ...rest }) => (
  <Route
    {...rest}
    render={(props) =>
      isLoggedIn ? (
        <Component {...props} />
      ) : (
        <Redirect to="/signin" />
      )
    }
  />
);
```

## Common Components Usage

### Layout Components
```javascript
import LayoutContentWrapper from "../utility/layoutWrapper";
import LayoutContent from "../utility/layoutContent";

<LayoutContentWrapper>
  <LayoutContent>
    {/* Your content */}
  </LayoutContent>
</LayoutContentWrapper>
```

### Data Tables
```javascript
import { Table } from "antd";

const columns = [
  {
    title: 'Name',
    dataIndex: 'name',
    key: 'name',
  },
  // More columns...
];

<Table 
  dataSource={data} 
  columns={columns} 
  loading={loading}
  pagination={{ pageSize: 10 }}
/>
```

### File Upload
```javascript
import { Upload, Button, Icon } from "antd";

<Upload
  action="/api/upload"
  listType="picture"
  beforeUpload={this.beforeUpload}
  onChange={this.handleUploadChange}
>
  <Button>
    <Icon type="upload" /> Upload
  </Button>
</Upload>
```

## Styling Guidelines

### Styled Components
```javascript
import styled from 'styled-components';

const StyledComponent = styled.div`
  padding: 20px;
  background: ${props => props.theme.backgroundColor};
`;
```

### CSS Classes
- Use existing Ant Design classes when possible
- Custom CSS in `src/static/css/`
- Component-specific styles in `.module.css` files

## API Integration Patterns

### Error Handling
```javascript
API.post("api/endpoint", data)
  .then(response => {
    if (response.status) {
      message.success(response.message);
      // Handle success
    } else {
      message.error(response.message);
    }
  })
  .catch(error => {
    message.error("An error occurred");
    console.error(error);
  });
```

### Loading States
```javascript
state = { loading: false };

// Before API call
this.setState({ loading: true });

// After API call
this.setState({ loading: false });

// In render
<Spin spinning={this.state.loading}>
  {/* Content */}
</Spin>
```

## Debugging Tips

### Common Issues
- **CORS errors**: Check API base URL configuration
- **Authentication failures**: Verify token storage and headers
- **Component not updating**: Check Redux state connections
- **Styling issues**: Verify CSS import order

### Development Tools
- Redux DevTools for state debugging
- React Developer Tools for component inspection
- Network tab for API call debugging
- Console for error tracking

## Build & Deployment

### Build Commands
```bash
# Development build
npm start

# Production build
npm run build

# Test
npm test
```

### Environment-Specific Builds
- Development: Uses `.env.development`
- Staging: Uses staging environment variables
- Production: Uses `.env.master`

## Performance Best Practices

### Code Splitting
```javascript
// Lazy loading components
const LazyComponent = React.lazy(() => import('./Component'));

// Usage with Suspense
<Suspense fallback={<div>Loading...</div>}>
  <LazyComponent />
</Suspense>
```

### Optimization Tips
- Use React.memo for expensive components
- Implement proper key props for lists
- Avoid inline functions in render methods
- Use useCallback and useMemo for optimization
- Implement proper error boundaries

This quick reference provides the essential information needed for day-to-day development on the MOVER Inventory CMS project.
