# Activity Logging Implementation Guide

## Overview
This guide explains how to implement comprehensive logging for the Mover Inventory & Storage System using the `activity_logs` table structure.

## Database Table Structure

### Primary Table: `activity_logs`
A single, comprehensive table that captures all user activities across both the APP and CMS platforms.

### Key Features
- **Flexible JSON storage** for activity-specific data
- **Comprehensive indexing** for fast queries
- **Built-in categorization** for different activity types
- **Before/after state tracking** for audit trails
- **Multi-platform support** (CMS, Mover Inventory APP, Mover Storage APP)

## Activity Categories & Types

### 1. Authentication (AUTH)
```sql
-- Login Event
activity_category = 'AUTH'
activity_type = 'USER_LOGIN'
activity_description = 'User logged into the system'
```

### 2. Shipment Management (SHIPMENT)
```sql
-- Shipment Creation
activity_category = 'SHIPMENT'
activity_type = 'SHIPMENT_CREATED'
activity_description = 'New shipment created'

-- Stage Access
activity_category = 'STAGE_ACCESS'
activity_type = 'STAGE_1_ACCESSED'
activity_description = 'User accessed Stage 1: Add Items to Inventory'
```

### 3. Inventory Operations (INVENTORY)
```sql
-- Item Addition
activity_category = 'INVENTORY'
activity_type = 'ITEMS_ADDED'
activity_description = 'Items added to inventory'
activity_data = '{"items_count": 5, "method": "manual_entry"}'

-- Item Edit
activity_category = 'INVENTORY'
activity_type = 'ITEM_EDITED'
activity_description = 'Inventory item modified'
before_state = '{"description": "Old desc", "volume": 10}'
after_state = '{"description": "New desc", "volume": 12}'
```

### 4. Storage Operations (STORAGE)
```sql
-- Item to Storage
activity_category = 'STORAGE'
activity_type = 'ITEMS_ADDED_TO_STORAGE'
activity_description = 'Items assigned to storage unit'
action_method = 'QR_SCAN' -- or 'MANUAL'
```

## Implementation Examples

### 1. APP Logging - Stage 1: Add Items to Inventory

```javascript
// When user accesses Stage 1
const logStageAccess = async (userId, shipmentId) => {
  await insertActivityLog({
    user_id: userId,
    user_email: userEmail,
    user_name: userName,
    user_type: userType,
    company_id: companyId,
    activity_category: 'STAGE_ACCESS',
    activity_type: 'STAGE_1_ACCESSED',
    activity_description: 'User accessed Stage 1: Add Items to Inventory',
    source_platform: 'MOVER_INVENTORY_APP',
    shipment_id: shipmentId,
    shipment_stage_id: 1,
    stage_name: 'Add Items to Inventory',
    activity_data: JSON.stringify({
      stage_number: 1,
      access_time: new Date().toISOString(),
      access_method: 'direct_navigation'
    }),
    status: 'SUCCESS',
    ip_address: userIpAddress
  });
};

// When user adds items
const logItemsAdded = async (userId, shipmentId, itemsData) => {
  await insertActivityLog({
    user_id: userId,
    activity_category: 'INVENTORY',
    activity_type: 'ITEMS_ADDED',
    activity_description: `User added ${itemsData.length} items to inventory`,
    source_platform: 'MOVER_INVENTORY_APP',
    shipment_id: shipmentId,
    items_count: itemsData.length,
    items_added: itemsData.length,
    activity_data: JSON.stringify({
      items: itemsData.map(item => ({
        id: item.id,
        description: item.description,
        room: item.room,
        volume: item.volume,
        weight: item.weight
      })),
      entry_method: 'manual'
    }),
    status: 'SUCCESS'
  });
};
```

### 2. APP Logging - Stage 2: Add Items to Storage

```javascript
// When user selects unit assignment method
const logStorageAssignment = async (userId, shipmentId, method, unitId, itemIds) => {
  await insertActivityLog({
    user_id: userId,
    activity_category: 'STORAGE',
    activity_type: 'ITEMS_ASSIGNED_TO_UNIT',
    activity_description: `Items assigned to storage unit via ${method}`,
    source_platform: 'MOVER_INVENTORY_APP',
    shipment_id: shipmentId,
    storage_unit_id: unitId,
    items_count: itemIds.length,
    action_method: method === 'qr_scan' ? 'QR_SCAN' : 'MANUAL',
    activity_data: JSON.stringify({
      unit_id: unitId,
      item_ids: itemIds,
      assignment_method: method,
      timestamp: new Date().toISOString()
    }),
    status: 'SUCCESS'
  });
};
```

### 3. CMS Logging - User Management

```javascript
// When user creates/edits another user
const logUserManagement = async (adminUserId, targetUserId, action, changes) => {
  await insertActivityLog({
    user_id: adminUserId,
    activity_category: 'USER_MGMT',
    activity_type: `USER_${action.toUpperCase()}`,
    activity_description: `User ${action} performed on user account`,
    source_platform: 'CMS',
    before_state: changes.before ? JSON.stringify(changes.before) : null,
    after_state: changes.after ? JSON.stringify(changes.after) : null,
    activity_data: JSON.stringify({
      target_user_id: targetUserId,
      action: action,
      fields_modified: changes.fields || [],
      reason: changes.reason || null
    }),
    status: 'SUCCESS'
  });
};
```

### 4. Signature Logging

```javascript
// When stage completion signature is captured
const logStageSignature = async (userId, shipmentId, stageId, signatureData) => {
  await insertActivityLog({
    user_id: userId,
    activity_category: 'SIGNATURE',
    activity_type: 'STAGE_SIGNATURE_CAPTURED',
    activity_description: `Digital signature captured for stage completion`,
    source_platform: 'MOVER_INVENTORY_APP',
    shipment_id: shipmentId,
    shipment_stage_id: stageId,
    activity_data: JSON.stringify({
      signature_type: 'stage_completion',
      signature_method: signatureData.method,
      items_summary: {
        total_items: signatureData.totalItems,
        added_to_inventory: signatureData.addedToInventory,
        added_to_storage: signatureData.addedToStorage,
        removed_from_storage: signatureData.removedFromStorage,
        removed_from_inventory: signatureData.removedFromInventory
      },
      signature_timestamp: new Date().toISOString()
    }),
    items_added: signatureData.addedToInventory,
    items_removed: signatureData.removedFromInventory,
    status: 'SUCCESS'
  });
};
```

## Query Examples for CMS Admin Interface

### 1. Get User Activity History
```sql
SELECT 
    al.*,
    u.first_name,
    u.last_name,
    c.company_name
FROM activity_logs al
JOIN users u ON al.user_id = u.id
JOIN companies c ON al.company_id = c.id
WHERE al.user_id = ? 
AND al.created_at BETWEEN ? AND ?
ORDER BY al.created_at DESC;
```

### 2. Get Shipment Activity Timeline
```sql
SELECT 
    al.*,
    u.first_name,
    u.last_name
FROM activity_logs al
JOIN users u ON al.user_id = u.id
WHERE al.shipment_id = ?
ORDER BY al.created_at ASC;
```

### 3. Get Stage-wise Activity Summary
```sql
SELECT 
    shipment_stage_id,
    stage_name,
    COUNT(*) as total_activities,
    COUNT(DISTINCT user_id) as unique_users,
    SUM(items_added) as total_items_added,
    SUM(items_removed) as total_items_removed
FROM activity_logs
WHERE shipment_id = ?
AND activity_category IN ('INVENTORY', 'STORAGE')
GROUP BY shipment_stage_id, stage_name
ORDER BY shipment_stage_id;
```

### 4. Get Activity by Date Range and Type
```sql
SELECT 
    DATE(created_at) as activity_date,
    activity_category,
    activity_type,
    COUNT(*) as activity_count,
    COUNT(DISTINCT user_id) as unique_users
FROM activity_logs
WHERE company_id = ?
AND created_at BETWEEN ? AND ?
GROUP BY DATE(created_at), activity_category, activity_type
ORDER BY activity_date DESC, activity_count DESC;
```

## Frontend Implementation for CMS

### Activity Log Viewer Component
```javascript
// React component for viewing activity logs
const ActivityLogViewer = () => {
  const [logs, setLogs] = useState([]);
  const [filters, setFilters] = useState({
    userId: null,
    dateRange: [null, null],
    activityCategory: null,
    activityType: null
  });

  const fetchLogs = async () => {
    const response = await API.post('api/admin/activity-logs/search', {
      data: filters
    });
    setLogs(response.data);
  };

  const columns = [
    {
      title: 'Date/Time',
      dataIndex: 'created_at',
      render: (date) => moment(date).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: 'User',
      dataIndex: 'user_name',
    },
    {
      title: 'Category',
      dataIndex: 'activity_category',
    },
    {
      title: 'Action',
      dataIndex: 'activity_type',
    },
    {
      title: 'Description',
      dataIndex: 'activity_description',
    },
    {
      title: 'Platform',
      dataIndex: 'source_platform',
    }
  ];

  return (
    <div>
      {/* Filter components */}
      <Table 
        dataSource={logs} 
        columns={columns}
        expandable={{
          expandedRowRender: record => (
            <div>
              <p><strong>Activity Data:</strong></p>
              <pre>{JSON.stringify(record.activity_data, null, 2)}</pre>
              {record.before_state && (
                <>
                  <p><strong>Before State:</strong></p>
                  <pre>{JSON.stringify(record.before_state, null, 2)}</pre>
                </>
              )}
              {record.after_state && (
                <>
                  <p><strong>After State:</strong></p>
                  <pre>{JSON.stringify(record.after_state, null, 2)}</pre>
                </>
              )}
            </div>
          )
        }}
      />
    </div>
  );
};
```

## Best Practices

### 1. Performance Considerations
- Use appropriate indexes for common query patterns
- Consider partitioning by date for large datasets
- Implement log rotation/archival for old data

### 2. Data Privacy
- Avoid logging sensitive personal information
- Implement proper access controls for log viewing
- Consider data retention policies

### 3. Error Handling
- Always log failed operations with error details
- Include context information for debugging
- Use appropriate status codes

### 4. Consistency
- Use standardized activity types and categories
- Maintain consistent JSON structure in activity_data
- Follow naming conventions for all log entries

This comprehensive logging system will provide full visibility into all user activities across the Mover Inventory & Storage System while maintaining performance and flexibility.
