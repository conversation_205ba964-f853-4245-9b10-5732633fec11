# MOVER Inventory CMS - Complete Project Documentation

## Project Overview

**MOVER Inventory CMS** is a comprehensive Content Management System designed for moving and logistics companies to manage their inventory, shipments, customers, and operations. This React-based web application provides a complete solution for tracking items throughout the moving process, from initial inventory to final delivery.

### Key Information
- **Project Name**: mover_inventory
- **Version**: 3.0.0
- **Technology Stack**: React 16.8.1, Redux, Ant Design, Material-UI
- **Architecture**: Single Page Application (SPA) with RESTful API integration

## System Architecture

### Frontend Architecture
- **Framework**: React 16.8.1 with functional and class components
- **State Management**: Redux with Redux Saga for side effects
- **UI Framework**: Ant Design 3.10.0 + Material-UI 4.11.2
- **Routing**: React Router 4.1.0 with Redux Router integration
- **Styling**: Styled Components + CSS modules

### Backend Integration
- **Primary API**: `moverinventory-api.movegistics.com`
- **Storage API**: `moverstorage.movegistics.com/api/`
- **File Storage**: AWS S3 buckets for document and image storage
- **Authentication**: JWT-based authentication with role-based access

## Core Business Entities

### 1. Companies
- **Purpose**: Multi-tenant system supporting multiple moving companies
- **Features**: Company profiles, integration keys, API access management
- **Relationships**: Has many staff, customers, shipments

### 2. Users/Staff
- **Roles**: 
  - Super Admin (MAINADMIN) - System-wide access
  - Company Super Admin (COMPANYSUPERADMIN) - Company-wide access
  - Regular Staff - Limited access based on permissions
- **Features**: User management, role assignment, password management

### 3. Groups
- **Purpose**: Organizational units within companies
- **Features**: Staff grouping, permission management

### 4. Customers
- **Purpose**: End customers who are moving
- **Features**: Customer profiles, contact information, shipment history
- **Portal**: Dedicated customer portal for shipment tracking

### 5. Shipments/Jobs
- **Purpose**: Core business entity representing moving jobs
- **Features**: 
  - Multi-stage workflow management
  - Inventory tracking per shipment
  - Document generation (PDFs, labels)
  - QR code generation for items
  - Photo documentation
  - Customer signatures

### 6. Inventory Items
- **Purpose**: Individual items being moved
- **Features**:
  - QR code tracking
  - Room assignment
  - Condition documentation
  - Photo attachments
  - Special handling flags (high value, electronics, firearms, etc.)

### 7. Shipment Types
- **Purpose**: Configurable workflow templates
- **Features**: 
  - Multi-stage process definition
  - Custom field configuration
  - Stage-specific permissions

### 8. Rooms & Items Lists
- **Purpose**: Master data for categorization
- **Features**: Predefined room types and item categories

### 9. Tags
- **Purpose**: Flexible labeling system
- **Features**: Color-coded tags for shipments and items

### 10. QR Codes & Labels
- **Purpose**: Physical tracking system
- **Features**: 
  - QR code generation
  - Label printing (including Dymo label support)
  - Generic label templates

## Key Features & Functionality

### Authentication & Authorization
- **Multi-level Authentication**: 
  - Initial login with email/password
  - Company selection for multi-company users
  - Role-based access control
- **User Types**:
  - Super Admin: Full system access
  - Company Admin: Company-wide access
  - Staff: Limited access based on role
  - Customers: Portal access for their shipments

### Dashboard & Analytics
- **Super Admin Dashboard**: System-wide statistics
- **Company Dashboard**: Company-specific metrics
- **Key Metrics**: 
  - Total companies, customers, shipments
  - Active vs inactive counts
  - Monthly shipment trends
  - Staff distribution by role

### Shipment Management
- **Workflow Engine**: Configurable multi-stage processes
- **Inventory Tracking**: Item-level tracking with QR codes
- **Document Generation**: 
  - PDF reports with company branding
  - Shipping labels
  - QR code labels
- **Photo Documentation**: Before/after photos with timestamps
- **Customer Interaction**: Digital signatures, customer portal access

### Integration Capabilities
- **Storage System Integration**: Seamless integration with warehouse management
- **Google Services**: Maps API for address validation
- **AWS S3**: File and image storage
- **External APIs**: RESTful API for third-party integrations
- **Label Printing**: Dymo label printer support

### Reporting & Documentation
- **PDF Generation**: Custom branded reports using React-PDF
- **CSV Export**: Data export capabilities
- **QR Code Generation**: Automatic QR code creation for tracking
- **Label Printing**: Physical label generation for items

## Technology Stack Details

### Core Dependencies
- **React Ecosystem**: React, React-DOM, React-Router, React-Redux
- **State Management**: Redux, Redux-Saga, Redux-Thunk
- **UI Components**: Ant Design, Material-UI
- **Styling**: Styled-Components, CSS modules
- **HTTP Client**: Axios for API communication
- **Date Handling**: Moment.js, Day.js, Date-fns

### Specialized Libraries
- **PDF Generation**: @react-pdf/renderer, React-to-print
- **QR Codes**: qrcode.react
- **Label Printing**: dymojs, react-dymo-hooks
- **File Handling**: file-saver, papaparse (CSV)
- **Rich Text**: draft-js, react-draft-wysiwyg
- **Maps**: react-places-autocomplete (Google Places)
- **Monitoring**: LogRocket for error tracking

### Development Tools
- **Build System**: Create React App (react-scripts 2.0.3)
- **Code Quality**: ESLint, Prettier (implied)
- **Deployment**: Jenkins CI/CD pipeline

## Environment Configuration

### Development Environment
- **API URL**: `staging.moverinventory-api.movegistics.com`
- **Storage API**: `storage-genine-api.movegistics.com/api/`
- **S3 Bucket**: `openxcell-development-public.s3.ap-south-1.amazonaws.com`

### Production Environment
- **API URL**: `moverinventory-api.movegistics.com`
- **Storage API**: `moverstorage.movegistics.com/api/`
- **S3 Bucket**: `mover-inventory-prod-public.s3.amazonaws.com`

## User Workflows

### Super Admin Workflow
1. System-wide dashboard access
2. Company management (create, edit, activate/deactivate)
3. Cross-company analytics and reporting
4. System configuration management

### Company Admin Workflow
1. Company-specific dashboard
2. Staff management within company
3. Customer management
4. Shipment oversight
5. Configuration of shipment types and workflows

### Staff Workflow
1. Daily shipment operations
2. Inventory management
3. Photo documentation
4. QR code scanning and label printing
5. Customer interaction management

### Customer Workflow
1. Portal access with shipment tracking
2. Inventory viewing
3. Photo access
4. Digital signature capabilities

## Security Features
- **JWT Authentication**: Secure token-based authentication
- **Role-based Access Control**: Granular permission system
- **Multi-tenant Security**: Company data isolation
- **API Key Management**: Secure external API access
- **Encrypted Storage**: Secure handling of sensitive data

## Integration Points
- **Warehouse Management**: Bi-directional sync with storage systems
- **Google Services**: Address validation and mapping
- **AWS Services**: File storage and content delivery
- **External APIs**: RESTful endpoints for third-party integration
- **Printing Services**: Direct integration with label printers

## API Architecture & Endpoints

### Primary API Structure
- **Base URL**: Environment-specific (staging/production)
- **Authentication**: Bearer token in Authorization header
- **Response Format**: JSON with standardized error codes
- **File Uploads**: Multipart form data support

### Key API Endpoints
- **Authentication**: `/api/admin/signin`, `/api/admin/signin-with-companyId`
- **Companies**: `/api/admin/company/*` (CRUD operations)
- **Staff**: `/api/admin/staff/*` (User management)
- **Customers**: `/api/admin/customer/*` (Customer management)
- **Shipments**: `/api/admin/job/*` (Shipment operations)
- **Inventory**: `/api/home/<USER>/*` (Item tracking)
- **QR Codes**: `/api/admin/qr_code/*` (QR code management)
- **Reports**: Various PDF generation endpoints

### External API Integrations
- **Storage API**: Warehouse management system integration
- **Google Places API**: Address validation and autocomplete
- **AWS S3**: File upload and storage
- **Dymo Label API**: Direct label printing

## File Structure Overview

### Source Code Organization
```
src/
├── api/                    # API handling and HTTP client
├── components/             # Reusable UI components
│   ├── company/           # Company management components
│   ├── customer/          # Customer management components
│   ├── job/               # Shipment/job components
│   ├── qrCode/            # QR code components
│   ├── GenericLabels/     # Label management
│   └── utility/           # Common utility components
├── containers/            # Page-level containers
│   ├── App/               # Main app container
│   ├── Page/              # Authentication pages
│   ├── Sidebar/           # Navigation sidebar
│   └── Topbar/            # Top navigation
├── redux/                 # State management
│   ├── auth/              # Authentication state
│   ├── app/               # Application state
│   └── store.js           # Redux store configuration
├── helpers/               # Utility functions
├── settings/              # Configuration and themes
└── static/                # Static assets and data
```

## Development Guidelines

### Code Standards
- **Component Structure**: Functional components preferred for new development
- **State Management**: Redux for global state, local state for component-specific data
- **Styling**: Styled-components for dynamic styling, CSS modules for static styles
- **API Calls**: Centralized through api-handler.js with error handling

### Performance Optimizations
- **Lazy Loading**: Async component loading with React.lazy
- **Code Splitting**: Route-based code splitting
- **Image Optimization**: S3-based image storage with CDN
- **Caching**: Local storage for user preferences and auth tokens

### Testing Strategy
- **Unit Tests**: Component-level testing with Jest
- **Integration Tests**: API integration testing
- **E2E Tests**: Critical user journey testing
- **Performance Monitoring**: LogRocket integration for production monitoring

## Deployment & DevOps

### CI/CD Pipeline
- **Build Tool**: Jenkins with automated builds
- **Environments**: Development, Staging, Production
- **Deployment**: Automated deployment to respective environments
- **Monitoring**: Real-time error tracking and performance monitoring

### Environment Management
- **Configuration**: Environment-specific .env files
- **Secrets Management**: Secure handling of API keys and tokens
- **Database**: Environment-specific database configurations
- **CDN**: CloudFront for static asset delivery

## Troubleshooting & Maintenance

### Common Issues
- **Authentication**: Token expiration and refresh handling
- **File Uploads**: S3 upload failures and retry mechanisms
- **QR Code Generation**: Printer connectivity issues
- **Performance**: Large dataset handling and pagination

### Monitoring & Logging
- **Error Tracking**: LogRocket for frontend error monitoring
- **API Monitoring**: Backend API health checks
- **Performance Metrics**: Page load times and user interaction tracking
- **User Analytics**: Usage patterns and feature adoption

## Future Enhancements

### Planned Features
- **Mobile App**: React Native mobile application
- **Advanced Analytics**: Enhanced reporting and dashboard features
- **AI Integration**: Automated item recognition and categorization
- **Real-time Updates**: WebSocket integration for live updates
- **Multi-language Support**: Internationalization framework

### Technical Improvements
- **React Upgrade**: Migration to latest React version
- **TypeScript**: Gradual migration to TypeScript
- **Modern State Management**: Consider React Query for server state
- **Performance**: Bundle size optimization and lazy loading improvements

This comprehensive documentation serves as a complete guide to understanding, developing, and maintaining the MOVER Inventory CMS system.
