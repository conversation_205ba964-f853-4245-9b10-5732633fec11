-- Migration: Create Activity Logs Table for Mover Inventory & Storage System
-- Purpose: Comprehensive logging for all user activities across APP and CMS
-- Date: 2025-01-03

CREATE TABLE activity_logs (
    -- Primary Key
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    
    -- User Information
    user_id INT NOT NULL,
    user_email VARCHAR(255) NOT NULL,
    user_name VARCHAR(255) NOT NULL,
    user_type ENUM('MAINADMIN', 'COMPANYSUPERADMIN', 'STAFF', 'CUSTOMER') NOT NULL,
    company_id INT NOT NULL,
    
    -- Activity Classification
    activity_category ENUM(
        'AUTH',           -- Login/Logout activities
        'SHIPMENT',       -- Shipment-related activities
        'INVENTORY',      -- Inventory management
        'STORAGE',        -- Storage operations
        'USER_MGMT',      -- User management
        'CUSTOMER_MGMT',  -- Customer management
        'SYSTEM_CONFIG',  -- System configuration changes
        'STAGE_ACCESS',   -- Stage access and modifications
        'QR_OPERATIONS',  -- QR code scanning operations
        'SIGNATURE',      -- Digital signature activities
        'REPORTS'         -- Report generation and access
    ) NOT NULL,
    
    activity_type VARCHAR(100) NOT NULL, -- Specific action type
    activity_description TEXT NOT NULL,  -- Detailed description of the activity
    
    -- Context Information
    shipment_id INT NULL,               -- Related shipment ID (if applicable)
    shipment_stage_id INT NULL,         -- Related shipment stage ID (if applicable)
    stage_name VARCHAR(255) NULL,       -- Stage name for better readability
    inventory_item_id INT NULL,         -- Related inventory item ID (if applicable)
    storage_unit_id INT NULL,           -- Related storage unit ID (if applicable)
    customer_id INT NULL,               -- Related customer ID (if applicable)
    
    -- Activity Details (JSON for flexible data storage)
    activity_data JSON NULL,            -- Stores additional activity-specific data
    
    -- Before/After States (for edit operations)
    before_state JSON NULL,             -- State before the change
    after_state JSON NULL,              -- State after the change
    
    -- Quantitative Data
    items_count INT NULL DEFAULT 0,     -- Number of items affected
    items_added INT NULL DEFAULT 0,     -- Items added count
    items_removed INT NULL DEFAULT 0,   -- Items removed count
    items_edited INT NULL DEFAULT 0,    -- Items edited count
    items_duplicated INT NULL DEFAULT 0, -- Items duplicated count
    
    -- Method Information
    action_method ENUM(
        'MANUAL',        -- Manual selection/entry
        'QR_SCAN',       -- QR code scanning
        'BULK_IMPORT',   -- Bulk import operation
        'API_CALL',      -- API-triggered action
        'SYSTEM_AUTO'    -- System automated action
    ) NULL,
    
    -- Source Information
    source_platform ENUM('CMS', 'MOVER_INVENTORY_APP', 'MOVER_STORAGE_APP') NOT NULL,
    ip_address VARCHAR(45) NULL,        -- User's IP address
    user_agent TEXT NULL,               -- Browser/app user agent
    device_info VARCHAR(255) NULL,      -- Device information
    
    -- Status and Results
    status ENUM('SUCCESS', 'FAILED', 'PARTIAL', 'PENDING') NOT NULL DEFAULT 'SUCCESS',
    error_message TEXT NULL,            -- Error details if status is FAILED
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    INDEX idx_user_id (user_id),
    INDEX idx_company_id (company_id),
    INDEX idx_activity_category (activity_category),
    INDEX idx_activity_type (activity_type),
    INDEX idx_shipment_id (shipment_id),
    INDEX idx_created_at (created_at),
    INDEX idx_source_platform (source_platform),
    INDEX idx_user_company_date (user_id, company_id, created_at),
    INDEX idx_shipment_stage (shipment_id, shipment_stage_id),
    INDEX idx_category_type_date (activity_category, activity_type, created_at),
    
    -- Foreign Key Constraints (adjust table names as per your schema)
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (shipment_id) REFERENCES shipments(id) ON DELETE SET NULL,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL
);

-- Create a view for easier querying of recent activities
CREATE VIEW recent_activity_logs AS
SELECT 
    al.*,
    u.first_name,
    u.last_name,
    c.company_name,
    s.shipment_name,
    cust.first_name as customer_first_name,
    cust.last_name as customer_last_name
FROM activity_logs al
LEFT JOIN users u ON al.user_id = u.id
LEFT JOIN companies c ON al.company_id = c.id
LEFT JOIN shipments s ON al.shipment_id = s.id
LEFT JOIN customers cust ON al.customer_id = cust.id
WHERE al.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
ORDER BY al.created_at DESC;

-- Create indexes for the view
CREATE INDEX idx_recent_logs_date ON activity_logs(created_at) 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY);

-- Sample data structure examples for activity_data JSON field:

-- Example 1: Stage Access Log
-- {
--   "stage_number": 1,
--   "stage_action": "ADD_ITEMS_TO_INVENTORY",
--   "access_method": "direct_navigation",
--   "session_duration": 1800
-- }

-- Example 2: Item Edit Log
-- {
--   "fields_modified": ["description", "volume", "weight"],
--   "old_values": {"description": "Old desc", "volume": 10, "weight": 5},
--   "new_values": {"description": "New desc", "volume": 12, "weight": 6},
--   "edit_reason": "customer_request"
-- }

-- Example 3: QR Code Scan Log
-- {
--   "qr_code": "QR123456789",
--   "scan_location": "warehouse_section_a",
--   "scan_device": "mobile_scanner_001",
--   "scan_accuracy": "high"
-- }

-- Example 4: Signature Log
-- {
--   "signature_type": "stage_completion",
--   "signature_method": "digital_pad",
--   "items_summary": {
--     "total_items": 25,
--     "added_to_inventory": 25,
--     "added_to_storage": 20,
--     "removed_from_storage": 5,
--     "removed_from_inventory": 0
--   }
-- }

-- Example 5: User Management Log
-- {
--   "action": "user_deactivated",
--   "target_user_id": 123,
--   "target_user_email": "<EMAIL>",
--   "deactivation_reason": "employment_terminated"
-- }

-- Create a stored procedure for easy log insertion
DELIMITER //
CREATE PROCEDURE InsertActivityLog(
    IN p_user_id INT,
    IN p_user_email VARCHAR(255),
    IN p_user_name VARCHAR(255),
    IN p_user_type VARCHAR(50),
    IN p_company_id INT,
    IN p_activity_category VARCHAR(50),
    IN p_activity_type VARCHAR(100),
    IN p_activity_description TEXT,
    IN p_source_platform VARCHAR(50),
    IN p_shipment_id INT,
    IN p_activity_data JSON,
    IN p_status VARCHAR(20),
    IN p_ip_address VARCHAR(45)
)
BEGIN
    INSERT INTO activity_logs (
        user_id, user_email, user_name, user_type, company_id,
        activity_category, activity_type, activity_description,
        source_platform, shipment_id, activity_data, status, ip_address
    ) VALUES (
        p_user_id, p_user_email, p_user_name, p_user_type, p_company_id,
        p_activity_category, p_activity_type, p_activity_description,
        p_source_platform, p_shipment_id, p_activity_data, p_status, p_ip_address
    );
END //
DELIMITER ;

-- Create a function to get activity summary for a user
DELIMITER //
CREATE FUNCTION GetUserActivityCount(p_user_id INT, p_days INT)
RETURNS INT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE activity_count INT;
    
    SELECT COUNT(*) INTO activity_count
    FROM activity_logs
    WHERE user_id = p_user_id
    AND created_at >= DATE_SUB(NOW(), INTERVAL p_days DAY);
    
    RETURN activity_count;
END //
DELIMITER ;

-- Add comments for documentation
ALTER TABLE activity_logs COMMENT = 'Comprehensive activity logging table for Mover Inventory & Storage System';

-- Grant appropriate permissions (adjust as needed for your environment)
-- GRANT SELECT, INSERT ON activity_logs TO 'app_user'@'%';
-- GRANT SELECT ON recent_activity_logs TO 'app_user'@'%';
-- GRANT EXECUTE ON PROCEDURE InsertActivityLog TO 'app_user'@'%';
-- GRANT EXECUTE ON FUNCTION GetUserActivityCount TO 'app_user'@'%';
